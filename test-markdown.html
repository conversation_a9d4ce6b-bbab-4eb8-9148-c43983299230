<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React Markdown 测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007acc;
            padding-bottom: 10px;
        }
        .selected-text {
            background-color: #ffeb3b;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>React Markdown 测试页面</h1>
    <p>这个页面用于测试Chrome扩展中的react-markdown功能是否正常工作。</p>
    
    <div class="test-section">
        <h2 class="test-title">测试文本 1 - 简单段落</h2>
        <p>这是一个简单的段落，用于测试基本的文本处理功能。选择这段文字并使用AI助手进行处理。</p>
    </div>

    <div class="test-section">
        <h2 class="test-title">测试文本 2 - 包含格式的文本</h2>
        <p>这段文字包含<strong>粗体</strong>、<em>斜体</em>和<code>代码</code>格式。测试AI助手是否能正确处理这些格式。</p>
    </div>

    <div class="test-section">
        <h2 class="test-title">测试文本 3 - 列表内容</h2>
        <ul>
            <li>第一个列表项</li>
            <li>第二个列表项</li>
            <li>第三个列表项</li>
        </ul>
    </div>

    <div class="test-section">
        <h2 class="test-title">测试文本 4 - 技术内容</h2>
        <p>React Markdown是一个用于在React应用中渲染Markdown内容的组件库。它支持CommonMark规范，并且可以通过插件扩展功能。</p>
    </div>

    <div class="test-section">
        <h2 class="test-title">测试文本 5 - 中英混合</h2>
        <p>This is a mixed content with both English and 中文内容。测试AI助手的多语言处理能力。</p>
    </div>

    <div class="test-section">
        <h2 class="test-title">使用说明</h2>
        <ol>
            <li>选择上面任意一段测试文本</li>
            <li>使用Chrome扩展的AI助手功能</li>
            <li>观察返回的Markdown内容是否正确渲染</li>
            <li>检查是否有任何错误信息</li>
        </ol>
    </div>

    <script>
        // 添加一些交互功能，方便测试
        document.addEventListener('mouseup', function() {
            const selection = window.getSelection();
            if (selection.toString().length > 0) {
                console.log('选中的文本:', selection.toString());
            }
        });
    </script>
</body>
</html>
